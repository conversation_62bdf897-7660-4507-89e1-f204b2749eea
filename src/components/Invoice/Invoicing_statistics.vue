<template>
  <div class="Equipment">
    <div class="Invoice" v-loading="div_loading">
      <!-- 搜索栏 -->
      <div class="search text-c">
        <el-row>
          <el-col :span="24" class="mt-10">
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 180px;"
                v-model="query_form.nickname"
                placeholder="用户昵称"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-select
                v-model="query_form.stage"
                filterable
                style="width: 140px;"
                size="small"
                placeholder="开票阶段"
              >
              
                <el-option label="用户发起申请" :value="0"></el-option>
                <el-option label="用户填写抬头完成" :value="1"></el-option>
                <el-option label="用户开具发票完成" :value="2"></el-option>
              </el-select>
            </span>

            <el-button @click="getTableData()" size="small" class="duck-green-button ml-5">查询</el-button>
            <el-button @click="reset_query_form(true)" type="info" size="small" class="ml-5">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作栏 -->
      <div>
        <el-card
          class="mt-10 mb-10"
          shadow="always"
          :body-style="{ padding: '8px' }"
          style="background: #f5fafe;"
        >
          <div class="cl">
            <el-tag type="info" class="f-r" effect="dark">共：{{query_form.total}} 条</el-tag>
            <span class="f-r mr-5">
              每页:
              <el-select
                v-model="query_form.limit"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option label="10" :value="10"></el-option>
                <el-option label="50" :value="50"></el-option>
                <el-option label="100" :value="100"></el-option>
                <el-option label="200" :value="200"></el-option>
                <el-option label="500" :value="500"></el-option>
                <el-option label="1000" :value="1000"></el-option>
                <el-option label="2000" :value="2000"></el-option>
                <el-option label="5000" :value="5000"></el-option>
                <el-option label="10000" :value="10000"></el-option>
              </el-select>条
            </span>
            <!-- <span class="f-r mr-5">
              排序:
              <el-select
                v-model="query_form.order_name"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option
                  v-for="item in sort_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="query_form.order_type"
                size="small"
                class="width-100"
                @change="getTableData()"
              >
                <el-option label="降序" value="desc"></el-option>
                <el-option label="升序" value="asc"></el-option>
              </el-select>
            </span>-->
          </div>
        </el-card>
      </div>
      <!-- 大表格 -->
      <div class="FormList">
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="id" label="订单发票申请ID" min-width="280"></el-table-column>
          <el-table-column prop="user_id" label="用户ID" width="250"></el-table-column>
          <el-table-column prop="nickname" label="用户昵称" width="240"></el-table-column>
          <el-table-column prop="total_amount" label="发票总金额" width="200">
            <template #default="{row}">
              <div>{{(row.total_amount / 100).toFixed(2)}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="开票次数" width="180"></el-table-column>
          <el-table-column prop="stage" label="阶段" width="200">
            <template #default="{row}">
              <div>{{ row.stage == "0"?'用户发起申请':(row.stage == '1'?"用户填写抬头完成":"开具发票完成")}}</div>
            </template>
          </el-table-column>

          <!-- <el-table-column
            fixed="right"
            class="operateForm"
            prop="operate"
            label="操作"
            min-width="210"
          >
            <template #default="{row}">
              <div>
                <el-button
                  class="duck-green-button"
                  @click="handleEdit(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >编辑</el-button>
                <el-button
                  class="duck-red-button"
                  @click="DelFn(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >删除</el-button>
              </div>
            </template>
          </el-table-column>-->
        </el-table>
      </div>

      <div class="Page">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :page-size="query_form.limit"
          :total="query_form.total"
          :pager-count="11"
          :current-page="query_form.page"
          @current-change="table_tiaozhuan"
          prev-text="上一页"
          next-text="下一页"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { InvoiceStatisticApi } from "@/request/api";
export default {
  data() {
    return {
      // 查询
      div_loading: true,
      tableData: [],
      // 显示当前页
      isShowCurrPage: true,
      title: "",
      // 弹框信息变量
      form: {},
      y_form: {
        id: "",
        nickname: "",
        title_type: null,
        title_name: "",
        taxpayer_id: "",
        stage: null,
        start_time: 0,
        end_time: 0,
        page: 1,
        limit: 10
      },
      query_form: {
        nickname: "",
        stage: null,
        page: 1,
        limit: 10
      },
      current_page: null,
      sort_list: [],

      phone: "",
      InvoiceHeader: "",
      value: "",
      title: "",
      // 表单校验
      rules: {
        GunId: [{ required: true, message: "枪编号不能为空", trigger: "blur" }],
        VirtualGID: [
          { required: true, message: "虚拟枪编号不能为空", trigger: "blur" }
        ],
        GunNum: [
          { required: true, message: "枪口号不能为空", trigger: "blur" }
        ],
        GunName: [
          { required: true, message: "枪名称不能为空", trigger: "blur" }
        ],
        PileId: [
          { required: true, message: "桩编号不能为空", trigger: "blur" }
        ],
        station: [
          { required: true, message: "所属场站不能为空", trigger: "blur" }
        ],
        Operator: [
          { required: true, message: "所属运营商不能为空", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    reset_query_form(e = false) {
      this.query_form = {
        nickname: "",
        stage: null,
        page: 1,
        limit: 10
      };
      if (e) {
        this.getTableData(1);
      }
    },
    table_tiaozhuan(val) {
      this.div_loading = true;
      // 改变默认的页数
      // 切换页码时，要获取每页显示的条数
      this.getTableData(val);
    },
    // 开票记录请求
    async getTableData(page = 1) {
      this.div_loading = true;
      this.query_form.page = page;
      let form = this.query_form;
      let PileRes = await InvoiceStatisticApi(form);
      if (!PileRes) return;
      this.tableData = PileRes.data.data;
      this.query_form.total = PileRes.data.total;
      this.current_page = PileRes.data.current_page;
      this.div_loading = false;
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang='less' scoped>
.Equipment {
  height: 100%;
  .Invoice {
    margin-top: 10px;
    box-shadow: 3px 0 5px #ccc;
    // 搜索栏
    // 搜索栏
    .search {
      .el-select {
        ::v-deep .el-input__inner {
          height: 30px;
          line-height: 30px;
        }
      }
      ::v-deep .el-input__inner {
        height: 30px;
        line-height: 30px;
      }
      ::v-deep .el-input__suffix {
        top: 4px;
      }
    }
    // 表格
    .FormList {
      margin-top: 20px;
      margin-left: 10px;
      ::v-deep .el-table .cell {
        text-align: center;
      }
      .operateForm {
        button {
          height: 50px;
        }
      }
    }
  }

  // 分页
  .Page {
    height: 45px;
    background-color: #fff;
    padding-top: 10px;
    padding-bottom: 10px;

    ::v-deep .el-pagination {
      text-align: center;
    }

    ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #009688;
    }
  }
}
</style>