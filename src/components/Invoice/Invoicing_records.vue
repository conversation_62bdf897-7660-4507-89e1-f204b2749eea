<template>
  <div class="Equipment">
    <div class="Invoice" v-loading="div_loading">
      <!-- 搜索栏 -->
      <div class="search text-c">
        <el-row>
          <el-col :span="24" class="mt-10">
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 140px;"
                v-model="query_form.id"
                placeholder="订单发票申请ID"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 120px;"
                v-model="query_form.nickname"
                placeholder="用户昵称"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-select
                v-model="query_form.title_type"
                filterable
                style="width: 140px;"
                size="small"
                placeholder="抬头类型"
              >
                <!-- <el-option label="全部" value></el-option> -->
                <el-option label="个人" :value="0"></el-option>
                <el-option label="单位" :value="1"></el-option>
              </el-select>
            </span>

            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 120px;"
                v-model="query_form.title_name"
                placeholder="抬头名称"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 120px;"
                v-model="query_form.taxpayer_id"
                placeholder="纳税人识别号"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-select
                v-model="query_form.stage"
                filterable
                style="width: 140px;"
                size="small"
                placeholder="开票阶段"
              >
                <!-- <el-option label="全部" value></el-option> -->
                <el-option label="用户发起申请" :value="0"></el-option>
                <el-option label="用户填写抬头完成" :value="1"></el-option>
                <el-option label="用户开具发票完成" :value="2"></el-option>
              </el-select>
            </span>
            <span class="select-box ml-5">
              <el-date-picker
                size="small"
                style="width: 200px;"
                v-model="query_form.start_time"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择开始时间"
              ></el-date-picker>
            </span>
            <span class="select-box ml-5">
              <el-date-picker
                size="small"
                style="width: 200px;"
                v-model="query_form.end_time"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择结束时间"
              ></el-date-picker>
            </span>

            <el-button @click="queryFn" size="small" class="duck-green-button ml-5">查询</el-button>
            <el-button @click="reset_query_form(true)" type="info" size="small" class="ml-5">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作栏 -->
      <div>
        <el-card
          class="mt-10 mb-10"
          shadow="always"
          :body-style="{ padding: '8px' }"
          style="background: #f5fafe;"
        >
          <div class="cl">
            <el-tag type="info" class="f-r" effect="dark">共：{{query_form.total}} 条</el-tag>
            <span class="f-r mr-5">
              每页:
              <el-select
                v-model="query_form.limit"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option label="10" :value="10"></el-option>
                <el-option label="50" :value="50"></el-option>
                <el-option label="100" :value="100"></el-option>
                <el-option label="200" :value="200"></el-option>
                <el-option label="500" :value="500"></el-option>
                <el-option label="1000" :value="1000"></el-option>
                <el-option label="2000" :value="2000"></el-option>
                <el-option label="5000" :value="5000"></el-option>
                <el-option label="10000" :value="10000"></el-option>
              </el-select>条
            </span>
            <!-- <span class="f-r mr-5">
              排序:
              <el-select
                v-model="query_form.order_name"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option
                  v-for="item in sort_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="query_form.order_type"
                size="small"
                class="width-100"
                @change="getTableData()"
              >
                <el-option label="降序" value="desc"></el-option>
                <el-option label="升序" value="asc"></el-option>
              </el-select>
            </span>-->
          </div>
        </el-card>
      </div>
      <!-- 大表格 -->
      <div class="FormList">
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="id" label="订单发票申请ID" min-width="290" align="center"></el-table-column>
          <el-table-column prop="user_id" label="用户ID" width="150" align="center"></el-table-column>
          <el-table-column prop="nickname" label="用户昵称" width="120" align="center"></el-table-column>
          <el-table-column prop="total_amount" label="发票总金额" width="120" align="center">
            <template #default="{row}">
              <div>{{(row.total_amount / 100).toFixed(2)}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="title_type" label="抬头类型" width="90" align="center">
            <template #default="{row}">
              <div>{{row.title_type=='0'?'个人':"单位"}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="title_name" label="抬头名称" width="200"></el-table-column>
          <el-table-column prop="taxpayer_id" label="纳税人识别号" width="200"></el-table-column>
          <el-table-column prop="stage" label="阶段" width="150">
            <template #default="{row}">
              <div>{{ row.stage == "0"?'用户发起申请':(row.stage == '1'?"用户填写抬头完成":"开具发票完成")}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="170">
            <template #default="{row}">
              <div>{{getTimestamp(row.create_time)}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="update_time" label="更新时间" width="170">
            <template #default="{row}">
              <div>{{getTimestamp(row.update_time)}}</div>
            </template>
          </el-table-column>
          <!-- <el-table-column fixed="right" class="operateForm" prop="operate" label="操作" width="210">
            <template #default="{row}">
              <div>
                <el-button
                  class="duck-green-button"
                  @click="handleEdit(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >编辑(无)</el-button>
                <el-button
                  class="duck-red-button"
                  @click="DelFn(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >删除(无)</el-button>
              </div>
            </template>
          </el-table-column>-->
        </el-table>
      </div>

      <div class="Page">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :page-size="query_form.limit"
          :total="query_form.total"
          :pager-count="11"
          :current-page="query_form.page"
          @current-change="table_tiaozhuan"
          prev-text="上一页"
          next-text="下一页"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { ElectronicInvoiceApi } from "@/request/api";
export default {
  data() {
    return {
      // 查询
      div_loading: true,
      tableData: [],
      // 显示当前页
      isShowCurrPage: true,
      title: "",
      // 弹框信息变量
      form: {},
      y_form: {
        id: "",
        user_id: null,
        nickname: null,
        total_amount: null,
        title_type: null,
        title_name: "",
        taxpayer_id: "",
        stage: null,
        create_time: null,
        update_time: null
      },
      query_form: {
        id: "",
        nickname: "",
        title_type: null,
        title_name: "",
        taxpayer_id: "",
        stage: null,
        start_time: "",
        end_time: "",
        page: 1,
        limit: 10
      },
      current_page: null,
      sort_list: [],

      phone: "",
      InvoiceHeader: "",
      value: "",
      title: "",
      form: {
        id: "",
        nickname: "",
        title_type: null,
        title_name: "",
        taxpayer_id: "",
        stage: null,
        start_time: 0,
        end_time: 0,
        page: 1,
        limit: 10
      },
      // 表单校验
      rules: {
        GunId: [{ required: true, message: "枪编号不能为空", trigger: "blur" }],
        VirtualGID: [
          { required: true, message: "虚拟枪编号不能为空", trigger: "blur" }
        ],
        GunNum: [
          { required: true, message: "枪口号不能为空", trigger: "blur" }
        ],
        GunName: [
          { required: true, message: "枪名称不能为空", trigger: "blur" }
        ],
        PileId: [
          { required: true, message: "桩编号不能为空", trigger: "blur" }
        ],
        station: [
          { required: true, message: "所属场站不能为空", trigger: "blur" }
        ],
        Operator: [
          { required: true, message: "所属运营商不能为空", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    reset_query_form(e = false) {
      this.query_form = {
        id: "",
        nickname: "",
        title_type: null,
        title_name: "",
        taxpayer_id: "",
        stage: null,
        start_time: "",
        end_time: "",
        page: 1,
        limit: 10
      };
      if (e) {
        this.getTableData(1);
      }
    },
    table_tiaozhuan(val) {
      this.div_loading = true;
      // 改变默认的页数
      // 切换页码时，要获取每页显示的条数
      this.getTableData(val);
    },
    //编辑
    handleEdit() {
      alert("暂无接口实现");
    },
    //删除
    DelFn() {
      alert("删除接口暂无");
    },
    // 开票记录请求
    async getTableData(page = 1) {
      this.div_loading = true;
      this.query_form.page = page;
      let form = this.query_form;
      let PileRes = await ElectronicInvoiceApi(form);
      if (!PileRes) return;
      this.tableData = PileRes.data.data;
      this.query_form.total = PileRes.data.total;
      this.current_page = PileRes.data.current_page;
      this.div_loading = false;
    },
    // 时间戳转换为年月日
    getTimestamp(time) {
      var date = new Date(time);
      var len = time.toString().length;
      if (len < 13) {
        var sub = 13 - len;
        sub = Math.pow(10, sub);
        date = new Date(time * sub);
      }
      var y = date.getFullYear() + "/";
      var M = date.getMonth() + 1;
      M = (M < 10 ? "0" + M : M) + "/";
      var d = date.getDate();
      d = (d < 10 ? "0" + d : d) + " ";
      var h = date.getHours();
      h = (h < 10 ? "0" + h : h) + ":";
      var m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      return y + M + d + h + m;
    },
    queryFn() {
      this.getTableData();
      // if (this.query_form.start_time === 0 && this.query_form.end_time === 0) {
      //   this.getTableData();
      // } else {
      //   const timeRegex = /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}$/;
      //   if (
      //     this.query_form.start_time !== 0 &&
      //     this.query_form.start_time !== "0"
      //   ) {
      //     if (!timeRegex.test(this.query_form.start_time)) {
      //       this.$message({
      //         message:
      //           "开始时间格式不正确，请输入正确的格式 如：2023/10/27 14:51",
      //         type: "error"
      //       });
      //       this.query_form.start_time = 0;
      //       return;
      //     }
      //     console.log("start_time年月日转换成时间戳");
      //     const startDate = new Date(this.query_form.start_time);
      //     this.query_form.start_time = Math.round(startDate.getTime() / 1000);
      //   } else {
      //     this.query_form.start_time = 0;
      //   }

      //   if (
      //     this.query_form.end_time !== 0 &&
      //     this.query_form.end_time !== "0"
      //   ) {
      //     if (!timeRegex.test(this.query_form.end_time)) {
      //       this.$message({
      //         message:
      //           "结束时间格式不正确，请输入正确的格式 如：2023/10/27 14:51",
      //         type: "error"
      //       });
      //       this.query_form.end_time = 0;
      //       return;
      //     }
      //     console.log("end_time年月日转换成时间戳");
      //     const endDate = new Date(this.query_form.end_time);
      //     this.query_form.end_time = Math.round(endDate.getTime() / 1000);
      //   } else {
      //     this.query_form.end_time = 0;
      //   }

      // setTimeout(() => {
      //   this.getTableData();
      // }, 1000);
      // }
    }
  },
  created() {
    this.getTableData();
  }
};
</script>

<style lang='less' scoped>
.Equipment {
  height: 100%;
  .Invoice {
    margin-top: 10px;
    box-shadow: 3px 0 5px #ccc;
    // 搜索栏
    // 搜索栏
    .search {
      .el-select {
        ::v-deep .el-input__inner {
          height: 30px;
          line-height: 30px;
        }
      }
      ::v-deep .el-input__inner {
        height: 30px;
        line-height: 30px;
      }
      ::v-deep .el-input__suffix {
        top: 4px;
      }
    }
    // 表格
    .FormList {
      margin-top: 20px;
      margin-left: 10px;
      ::v-deep .el-table .cell {
        text-align: center;
      }
      .operateForm {
        button {
          height: 50px;
        }
      }
    }
  }

  // 分页
  .Page {
    height: 45px;
    background-color: #fff;
    padding-top: 10px;
    padding-bottom: 10px;

    ::v-deep .el-pagination {
      text-align: center;
    }

    ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #009688;
    }
  }
}
</style>
