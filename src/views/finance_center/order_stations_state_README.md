# 场站状态管理页面

## 页面位置
`src/views/finance_center/order_stations_state.vue`

## 功能描述
场站状态管理页面用于展示和管理充电场站的运营状态数据，包括电量、电价、服务费等关键信息。

## 主要功能

### 1. 数据展示
- **场站名称**: 显示充电场站的名称
- **运营商**: 显示场站所属的运营商
- **电量**: 显示电量数据（单位：kWh，保留2位小数）
- **电价**: 显示电价信息（单位：元，保留4位小数）
- **服务费**: 显示服务费（单位：元，保留4位小数）
- **统计时间**: 显示数据统计的时间范围
- **服务费分成占比**: 显示新能分成占比（保留2位小数）
- **创建时间**: 记录创建时间

### 2. 查询功能
- **场站名称搜索**: 支持按场站名称模糊搜索
- **运营商筛选**: 支持按运营商进行筛选（超级管理员可见）
- **重置功能**: 一键重置查询条件

### 3. 数据操作
- **编辑功能**: 支持编辑场站状态信息
  - 场站名称
  - 电量（kWh）
  - 电价（元）
  - 服务费（元）
  - 统计时间
  - 服务费分成占比（%）
- **删除功能**: 支持删除场站状态记录
- **导出Excel**: 支持导出数据到Excel文件（TODO: 待实现）

### 4. 分页功能
- 支持分页显示数据
- 可自定义每页显示条数
- 显示总记录数

## API接口

### 已实现的API
1. **获取场站状态列表**
   - 接口: `POST /v1/admin/admin/order_stations_state/list`
   - 功能: 获取场站状态数据列表

2. **更新场站状态**
   - 接口: `POST /v1/admin/admin/order_stations_state/update`
   - 功能: 更新场站状态信息

3. **删除场站状态**
   - 接口: `POST /v1/admin/admin/order_stations_state/delete`
   - 功能: 删除场站状态记录

### 待实现的API
4. **导出Excel**
   - 接口: `POST /v1/admin/admin/order_stations_state/export`
   - 功能: 导出场站状态数据到Excel文件
   - 状态: TODO - 暂未实现

## 数据格式说明

### 数据库存储格式
- `electricity_total`: 电量，存储时保留2位小数（乘以100）
- `electricity_price`: 电价，存储时保留4位小数（乘以10000）
- `service_price`: 服务费，存储时保留4位小数（乘以10000）
- `ratio_ser_price`: 服务费分成占比，存储时保留2位小数（乘以100）

### 前端显示格式
- 电量: 显示为实际值，保留2位小数
- 电价: 显示为实际值，保留4位小数
- 服务费: 显示为实际值，保留4位小数
- 分成占比: 显示为百分比，保留2位小数

## 权限控制
- 运营商筛选功能仅对超级管理员（pid <= 0）可见
- 普通运营商用户只能查看自己的数据

## 样式特点
- 使用项目统一的样式风格
- 响应式布局，适配不同屏幕尺寸
- 表格数据居中对齐
- 金额数据使用红色高亮显示

## 使用说明
1. 进入页面后自动加载数据
2. 使用搜索条件筛选数据
3. 点击"编辑"按钮修改记录
4. 点击"删除"按钮删除记录（需确认）
5. 点击"导出Excel"按钮导出数据（功能待实现）

## 注意事项
- 删除操作不可恢复，请谨慎操作
- 编辑时数据会自动进行格式转换
- 导出Excel功能目前为占位实现，需要后续开发
