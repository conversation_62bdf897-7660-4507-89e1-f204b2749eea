<template>
  <div class="Equipment">
    <!-- 面包屑 -->
    <div class="crumb">
      <Crumb ref="crumb"></Crumb>
    </div>
    <div class="form" v-loading="div_loading">
      <!-- 头部选择栏 -->
      <ul class="header">
        <li :class="{active:isShowCurrPage}">充值订单</li>
      </ul>
      <!-- 搜索栏 -->
      <div class="search text-c">
        <el-row>
          <el-col :span="24" class="mt-10">
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 150px;"
                v-model="query_form.id"
                placeholder="充值流水号"
                clearable
              ></el-input>
            </span>

            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 150px;"
                v-model="query_form.trade_no"
                placeholder="外部订单编号"
                clearable
              ></el-input>
            </span>
            <span class="select-box ml-5">
              状态:
              <el-select
                v-model="query_form.state"
                filterable
                style="width: 150px;"
                size="small"
                multiple
                placeholder="状态"
              >
                <el-option
                  v-for="v in stateOption"
                  :key="'查询充值订单状态'+v.value"
                  :label="v.label"
                  :value="v.value"
                ></el-option>
              </el-select>
            </span>
            <span class="select-box ml-5">
              加余额结果:
              <el-select
                v-model="query_form.add_balance"
                filterable
                style="width: 150px;"
                size="small"
                placeholder="余额结果"
              >
                <el-option label="全部" :value="0"></el-option>
                <el-option
                  v-for="v in BalanceOption"
                  :key="'查询余额结果'+v.value"
                  :label="v.label"
                  :value="v.value"
                ></el-option>
              </el-select>
            </span>
            <span class="select-box ml-5">
              支付类型:
              <el-select
                v-model="query_form.type"
                filterable
                style="width: 150px;"
                size="small"
                placeholder="支付类型"
              >
                <el-option label="全部" :value="0"></el-option>
                <el-option
                  v-for="v in TypeOption"
                  :key="'查询支付类型'+v.value"
                  :label="v.label"
                  :value="v.value"
                ></el-option>
              </el-select>
            </span>
            <span class="select-box ml-5">
              退款状态:
              <el-select
                v-model="query_form.refund_state"
                filterable
                style="width: 200px;"
                size="small"
                multiple
                placeholder="退款状态"
              >
                <el-option
                  v-for="v in RefundOption"
                  :key="'查询退款状态'+v.value"
                  :label="v.label"
                  :value="v.value"
                ></el-option>
              </el-select>
            </span>
            <span class="select-box ml-5">
              日期范围:
              <el-date-picker
                value-format="yyyy-MM-dd"
                style="width: 220px;"
                v-model="query_form.time_range"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :clearable="false"
                :picker-options="picker_options"
              ></el-date-picker>
            </span>
            <el-button @click="getTableData()" size="small" class="duck-green-button ml-5">查询</el-button>
            <el-button @click="reset_query_form(true)" type="info" size="small" class="ml-5">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作栏 -->
      <div>
        <el-card
          class="mt-10 mb-10"
          shadow="always"
          :body-style="{ padding: '8px' }"
          style="background: #f5fafe;"
        >
          <div class="cl">
            <el-tag type="info" class="f-r" effect="dark">共：{{query_form.total}} 条</el-tag>
            <span class="f-r mr-5">
              每页:
              <el-select
                v-model="query_form.limit"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option label="10" :value="10"></el-option>
                <el-option label="50" :value="50"></el-option>
                <el-option label="100" :value="100"></el-option>
                <el-option label="200" :value="200"></el-option>
                <el-option label="500" :value="500"></el-option>
                <el-option label="1000" :value="1000"></el-option>
                <el-option label="2000" :value="2000"></el-option>
                <el-option label="5000" :value="5000"></el-option>
                <el-option label="10000" :value="10000"></el-option>
              </el-select>条
            </span>
            <span class="f-r mr-5">
              排序:
              <el-select
                v-model="query_form.order_name"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option
                  v-for="item in sort_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="query_form.order_type"
                size="small"
                class="width-100"
                @change="getTableData()"
              >
                <el-option label="降序" value="desc"></el-option>
                <el-option label="升序" value="asc"></el-option>
              </el-select>
            </span>
          </div>
        </el-card>
      </div>

      <!-- 大表格 -->
      <div class="Table_Form">
        <el-table style="width:100%" :data="tableData" border>
          <el-table-column prop="id" label="充值流水号" width="290" align="center"></el-table-column>
          <el-table-column prop="trade_no" label="外部订单编号" width="260" align="center"></el-table-column>
          <el-table-column prop="name" label="名称" width="110" align="center"></el-table-column>
          <el-table-column prop="phone" label="用户手机号" width="120" align="center"></el-table-column>
          <el-table-column prop="price" label="支付金额" width="90" align="center">
            <template #default="{row}">
              <div>{{row.price / 100}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="currency" label="币种" width="90" align="center">
            <template #default="{row}">
              <div>{{row.currency==1?"CNY":"USD"}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="state" label="状态" width="90" align="center">
            <template #default="{row}">
              <div>{{row.state==1?"生成订单":(row.state==2?"支付中":(row.state==3?"完成支付":(row.state==4?"失败或其他问题":"关闭支付")))}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="add_balance" label="加余额结果" width="100" align="center">
            <template #default="{row}">
              <div>{{row.add_balance==1?"成功":(row.add_balance==2?"失败":"没有加币")}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="支付类型" width="90" align="center">
            <template #default="{row}">
              <div>{{row.type==1?"充值余额":"充卡"}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="refund_state" label="退款状态" width="90" align="center">
            <template #default="{row}">
              <div>{{row.refund_state==1?"无退款":(row.refund_state==2?"申请退款":(row.refund_state==3?"已退款":(row.refund_state==4?"退款失败":(row.refund_state==10?"已拒绝":"已取消"))))}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="160" align="center"></el-table-column>

          <el-table-column prop="title" label="充值项" width="200" align="center"></el-table-column>

          <el-table-column fixed="right" label="操作" min-width="260" align="center">
            <template #default="{row}">
              <div>
                <el-button
                  class="duck-yellow-button"
                  @click="RefundInfo(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >详情</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        :page-size="query_form.limit"
        :total="query_form.total"
        :current-page="query_form.page"
        @page-change="table_tiaozhuan"
      ></pagination>

      <!-- 视图弹框 -->
      <el-dialog
        v-if="ViewdialogVisible"
        class="ViewBox"
        :visible.sync="ViewdialogVisible"
        width="48%"
      >
        <div>
          <el-tabs v-model="activeName">
            <el-tab-pane label="详情" name="first" class="first">
              <el-collapse v-model="activeNames">
                <el-collapse-item title="基本信息" name="1">
                  <el-form ref="form" label-position="left" inline class="demo-table-expand">
                    <el-form-item label="充值流水号:">
                      <span>{{BaseData.id}}</span>
                    </el-form-item>
                    <el-form-item label="外部订单编号 :">
                      <span>{{BaseData.trade_no}}</span>
                    </el-form-item>
                    <el-form-item label="支付项id:">
                      <span>{{BaseData.name}}</span>
                    </el-form-item>
                    <el-form-item label="支付金额(元):">
                      <span>{{BaseData.price / 100}}</span>
                    </el-form-item>
                    <el-form-item label="币种:">
                      <span>{{BaseData.currency==1?"CNY":"USD"}}</span>
                    </el-form-item>
                    <el-form-item label="状态:">
                      <span>{{BaseData.state==1?"生成订单":(BaseData.state==2?"支付中":(BaseData.state==3?"完成支付":(BaseData.state==4?"失败或其他问题":"关闭支付")))}}</span>
                    </el-form-item>
                    <el-form-item label="客户ip:">
                      <span>{{BaseData.ip}}</span>
                    </el-form-item>
                    <el-form-item label="结果信息:">
                      <span>{{BaseData.msg}}</span>
                    </el-form-item>
                    <el-form-item label="支付类型:">
                      <span>{{BaseData.type==1?"充值余额":"充卡"}}</span>
                    </el-form-item>
                    <el-form-item label="退款状态:">
                      <span>{{BaseData.refund_state==1?"无退款":(BaseData.refund_state==2?"申请退款":(BaseData.refund_state==3?"已退款":(BaseData.refund_state==4?"退款失败":(BaseData.refund_state==10?"已拒绝":"已取消"))))}}</span>
                    </el-form-item>
                    <el-form-item label="创建时间:">
                      <span>{{BaseData.create_time}}</span>
                    </el-form-item>
                    <el-form-item label="更新时间:">
                      <span>{{BaseData.update_time}}</span>
                    </el-form-item>
                    <el-form-item label="用户id:">
                      <span>{{BaseData.user_id}}</span>
                    </el-form-item>
                    <el-form-item label="用户昵称:">
                      <span>{{BaseData.nickname}}</span>
                    </el-form-item>
                    <el-form-item label="用户手机号:">
                      <span>{{BaseData.phone}}</span>
                    </el-form-item>
                    <el-form-item label="支付项id:">
                      <span>{{BaseData.list_id}}</span>
                    </el-form-item>
                    <el-form-item label="支付项:">
                      <span>{{BaseData.title}}</span>
                    </el-form-item>
                    <el-form-item label="回调列表:" style="width:100%">
                      <span>{{BaseData.callback_list}}</span>
                    </el-form-item>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-dialog>
    </div>
  </div>
</template> 
    <script>
import { pickerOptions, queryFormLimit } from "@/common.js";
import Crumb from "@/views/home/<USER>";
import pagination from "@/components/pagination.vue";
import {
  RechargeOrderInfoApi,
  RechargeOrderListApi,
  RechargeOrderSortListApi
} from "@/request/api";
export default {
  components: {
    pagination,
    Crumb
  },
  data() {
    return {
      ViewdialogVisible: false,
      picker_options: pickerOptions,
      query_form_limit: queryFormLimit,
      activeNames: ["1"],
      //选项卡
      activeName: "first",
      // 基本信息
      BaseData: [],
      // 查询
      div_loading: true,
      title: "",
      tableData: [],
      CallbackData: [],
      // 显示当前页
      isShowCurrPage: true,
      query_form: {
        id: "",
        user_id: null,
        trade_no: "",
        list_id: "",
        state: "",
        add_balance: "",
        type: "",
        refund_state: "",
        time_range: [],
        page: 1,
        limit: 10,
        order_name: "a.create_time",
        order_type: "desc"
      },
      current_page: null,
      sort_list: [],
      phone: "",
      value: "",
      corp_id: null,
      Total_pay_money: null,
      Total_electricity: null,
      //退款订单状态
      stateOption: [
        { value: 1, label: "生成订单" },
        { value: 2, label: "支付中" },
        { value: 3, label: "完成支付" },
        { value: 4, label: "失败或其他问题" },
        { value: 5, label: "关闭支付" }
      ],
      BalanceOption: [
        { value: 1, label: "成功" },
        { value: 2, label: "失败" },
        { value: 3, label: "没有加币" }
      ],
      TypeOption: [
        { value: 1, label: "充值余额" },
        { value: 2, label: "充卡" }
      ],
      RefundOption: [
        { value: 1, label: "无退款" },
        { value: 2, label: "申请退款" },
        { value: 3, label: "已退款" },
        { value: 4, label: "退款失败" },
        { value: 10, label: "已拒绝" },
        { value: 11, label: "已取消" }
      ]
    };
  },

  methods: {
    // 排序
    async SortListInfoFn() {
      let res = await RechargeOrderSortListApi();
      if (!res) return;
      this.sort_list = res.data.sort_list;
      this.query_form.order_name = res.data.order_name;
      this.query_form.order_type = res.data.order_type;
    },
    reset_query_form(e = false) {
      this.query_form = {
        id: "",
        user_id: null,
        trade_no: "",
        list_id: "",
        state: "",
        add_balance: "",
        type: "",
        refund_state: "",
        time_range: [],
        page: 1,
        limit: 10,
        order_name: "a.create_time",
        order_type: "desc"
      };
      if (e) {
        this.getTableData(1);
      }
    },
    table_tiaozhuan(val) {
      this.query_form.status = [];
      this.div_loading = true;
      this.getTableData(val);
    },
    // 详情
    async RefundInfo(row) {
      this.title = "详情";
      let res = await RechargeOrderInfoApi({ recharge_id: row.id });
      if (!res) return;
      if (res.code == 200) {
        this.ViewdialogVisible = true;
      }
      this.BaseData = res.data;
      this.CallbackData = res.data.callback_list;
      console.log("回调信息", this.CallbackData);
      console.log("this.form", this.form);
    },
    // 充值订单请求
    async getTableData(page = 1) {
      console.log("this.query_form", this.query_form);
      this.div_loading = true;
      this.query_form.page = page;
      let form = this.query_form;
      let Res = await RechargeOrderListApi(form);
      if (!Res) return;
      this.tableData = Res.data.data;
      this.query_form.total = Res.data.total;
      this.current_page = Res.data.current_page;
      this.div_loading = false;
    }
  },
  created() {
    this.getTableData();
    this.SortListInfoFn();
  }
};
</script>
    
    <style lang="less" scoped>
.Equipment {
  height: 100%;

  .MenuIcon {
    .IconBox {
      border: 1px solid #dcdfe6;
      display: inline-block;
      width: 50px;
      text-align: center;
      i {
        font-size: 22px;
        vertical-align: middle;
      }
    }
    .DropdownBtn {
      border: 1px solid #dcdfe6;
      display: inline-block;
      width: 50px;
      text-align: center;
      box-sizing: border-box;
    }
  }

  .crumb {
  }

  .form {
    box-shadow: 3px 0 5px #ccc;

    // 头部
    .header {
      display: flex;
      height: 40px;
      line-height: 40px;
      background-color: #f5f7fa;
      margin-bottom: 10px;

      li {
        border: 1px solid #f5f7fa;
        background-color: #fff;
        border-radius: 5px;
        padding: 0 10px;
        cursor: pointer;
        color: #cacdd2;

        &.active {
          color: #009688;
        }
      }
    }

    // 表格
    .Table_Form {
      margin-left: 10px;
      .ellipsis-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ellipsis-id {
        white-space: nowrap;
      }
      .el-input-number--small {
        width: 50px;
      }
      .amountBox {
        display: flex;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        .sum_footer_totalMoney {
          display: flex;
          margin-left: 10px;
        }
        .sum_footer_totalElect {
          display: flex;
          margin-right: 20px;
          width: 238px;
        }
        .sum_footer_money {
          width: 122px;
        }
        .sum_footer_money_num {
          color: #000;
          font-size: 20px;
          font-weight: 600;
        }

        .sum_footer_electricity_num {
          color: #000;
          font-size: 20px;
          font-weight: 600;
        }
      }
    }
    .Page {
      height: 45px;
      background-color: #fff;
      padding-top: 10px;
      padding-bottom: 10px;

      ::v-deep .el-pagination {
        text-align: center;
      }

      ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #009688;
      }
    }

    .el-radio {
      margin-right: 10px;
    }
    .ViewBox {
      ::v-deep .el-tabs__item:hover {
        color: #009688 !important;
      }
      ::v-deep .is-active {
        color: #009688 !important;
      }
      ::v-deep .el-tabs__active-bar {
        background-color: #009688;
      }

      .demo-table-expand {
        font-size: 0;
      }
      .demo-table-expand label {
        width: 90px;
        color: #99a9bf;
      }
      .demo-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
      }
      .second {
        .btn {
          margin-left: 20px;
          width: 50px;
          border: 1px solid #eee;
          border-radius: 4px;
          padding: 5px;
          background-color: #fff;
        }
      }
    }
  }
}
</style>