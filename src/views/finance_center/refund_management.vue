<template>
  <div class="Equipment">
    <!-- 面包屑 -->
    <div class="crumb">
      <Crumb ref="crumb"></Crumb>
    </div>
    <div class="form" v-loading="div_loading">
      <!-- 搜索栏 -->
      <div class="search text-c">
        <el-row>
          <el-col :span="24" class="mt-10">
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 200px;"
                v-model="query_form.id"
                placeholder="退款流水号"
                clearable
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 200px;"
                v-model.number="query_form.user_id"
                clearable
                placeholder="用户id"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 200px;"
                v-model.number="query_form.phone"
                placeholder="用户手机号"
                clearable
              ></el-input>
            </span>
            <span class="select-box ml-5">
              <el-input
                size="small"
                style="width: 200px;"
                v-model="query_form.order_id"
                clearable
                placeholder="充值订单号"
              ></el-input>
            </span>
            <span class="select-box ml-5">
              状态:
              <el-select
                v-model="query_form.state"
                filterable
                style="width: 200px;"
                size="small"
                multiple
                placeholder="状态"
              >
                <el-option
                  v-for="v in stateOption"
                  :key="'查询退款订单状态'+v.value"
                  :label="v.label"
                  :value="v.value"
                ></el-option>
              </el-select>
            </span>
            <span class="select-box ml-5">
              日期:
              <el-date-picker
                value-format="yyyy-MM-dd"
                style="width: 220px;"
                v-model="query_form.time_range"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :clearable="false"
                :picker-options="picker_options"
              ></el-date-picker>
            </span>
            <el-button @click="getTableData()" size="small" class="duck-green-button ml-5">查询</el-button>
            <el-button @click="reset_query_form(true)" type="info" size="small" class="ml-5">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作栏 -->
      <div>
        <el-card
          class="mt-10 mb-10"
          shadow="always"
          :body-style="{ padding: '8px' }"
          style="background: #f5fafe;"
        >
          <div class="cl">
            <el-tag type="info" class="f-r" effect="dark">共：{{query_form.total}} 条</el-tag>
            <span class="f-r mr-5">
              每页:
              <el-select
                v-model="query_form.limit"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option label="10" :value="10"></el-option>
                <el-option label="50" :value="50"></el-option>
                <el-option label="100" :value="100"></el-option>
                <el-option label="200" :value="200"></el-option>
                <el-option label="500" :value="500"></el-option>
                <el-option label="1000" :value="1000"></el-option>
                <el-option label="2000" :value="2000"></el-option>
                <el-option label="5000" :value="5000"></el-option>
                <el-option label="10000" :value="10000"></el-option>
              </el-select>条
            </span>
            <span class="f-r mr-5">
              排序:
              <el-select
                v-model="query_form.order_name"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option
                  v-for="item in sort_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="query_form.order_type"
                size="small"
                class="width-100"
                @change="getTableData()"
              >
                <el-option label="降序" value="desc"></el-option>
                <el-option label="升序" value="asc"></el-option>
              </el-select>
            </span>
          </div>
        </el-card>
      </div>

      <!-- 大表格 -->
      <div class="Table_Form">
        <el-table style="width:100%" :data="tableData" border>
          <el-table-column prop="id" label="退款流水号" width="290" align="center"></el-table-column>
          <el-table-column prop="user_id" label="用户id" width="150" align="center"></el-table-column>
          <el-table-column prop="phone" label="用户手机号" width="120" align="center"></el-table-column>
          <el-table-column prop="state" label="状态" min-width="90" align="center">
            <template #default="{row}">
              <div>{{ row.state == "1"?'已申请':(row.state == '5'?"退款中":(row.state == '10'?"已拒绝":(row.state == '11'?"已取消":(row.state == '20'?"已退款":(row.state == '30'?"退款失败":"其他")))))}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="refund_screenshot_url"
            label="退款截图链接"
            min-width="180"
            align="center"
          >
            <template #default="{row}">
              <!-- <div>{{ row.refund_screenshot_url == null?'':row.refund_screenshot_url}}</div> -->
              <el-tooltip
                class="item"
                effect="light"
                v-if="row.refund_screenshot_url"
                placement="top"
              >
                <div slot="content" style="text-align:center;min-width:150px;">
                  <img :src="row.refund_screenshot_url" style="width:150px;" />
                </div>
                <div>
                  <img :src="row.refund_screenshot_url" width="30" height="30" />
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column prop="refund_price" label="退款金额" width="120" align="center">
            <template #default="{row}">
              <div>{{row.refund_price/100}}元</div>
            </template>
          </el-table-column>
          <el-table-column prop="order_price" label="订单总金额" width="130" align="center">
            <template #default="{row}">
              <div>{{row.order_price/100}}元</div>
            </template>
          </el-table-column>
          <el-table-column prop="payer_refund" label="用户退款金额" width="120" align="center">
            <template #default="{row}">
              <div>{{row.payer_refund/100}}元</div>
            </template>
          </el-table-column>
          <el-table-column prop="order_id" label="充值订单号" width="290" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="160" align="center"></el-table-column>
          <el-table-column prop="success_time" label="退款成功时间" width="160" align="center"></el-table-column>

          <el-table-column prop="currency" label="币种" width="90" align="center">
            <template #default="{row}">
              <div>{{row.currency==1?"CNY":"USD"}}</div>
            </template>
          </el-table-column>

          <el-table-column prop="channel" label="退款渠道" min-width="90" align="center">
            <template #default="{row}">
              <div>{{ row.channel == "ORIGINAL"?'原路退款':(row.channel == 'BALANCE'?"退回到余额":(row.channel == 'OTHER_BALANCE'?"原账户异常退到其他余额账户":(row.channel == 'OTHER_BANKCARD'?"原银行卡异常退到其他银行卡":"其他")))}}</div>
            </template>
          </el-table-column>

          <!-- <el-table-column prop="reason_for_stop_text" label="停止原因文本" width="180" align="center">
            <template slot-scope="scope">
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.reason_for_stop_text"
                placement="top"
              >
                <div class="ellipsis-text">{{ scope.row.reason_for_stop_text }}</div>
              </el-tooltip>
            </template>
          </el-table-column>-->
          <el-table-column fixed="right" label="操作" min-width="260" align="center">
            <template #default="{row}">
              <div>
                <!-- v-if="row.state == 1" -->
                <el-button
                  class="duck-green-button"
                  v-if="row.state == 1"
                  @click="StatisticsFn(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >审核</el-button>
                <el-button
                  class="duck-yellow-button"
                  @click="RefundInfo(row)"
                  style="border-radius: 2px;border:none;margin-left: 8px;padding: 5px 10px"
                >详情</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        :page-size="query_form.limit"
        :total="query_form.total"
        :current-page="query_form.page"
        @page-change="table_tiaozhuan"
      ></pagination>

      <!-- 视图弹框 -->
      <el-dialog
        v-if="ViewdialogVisible"
        class="ViewBox"
        :visible.sync="ViewdialogVisible"
        width="48%"
      >
        <div>
          <el-tabs v-model="activeName">
            <el-tab-pane label="详情" name="first" class="first">
              <el-collapse v-model="activeNames">
                <el-collapse-item title="基本信息" name="1">
                  <el-form ref="form" label-position="left" inline class="demo-table-expand">
                    <el-form-item label="退款流水号:">
                      <span>{{BaseData.order_id}}</span>
                    </el-form-item>
                    <el-form-item label="外部退款号:">
                      <span>{{BaseData.out_refund_id}}</span>
                    </el-form-item>
                    <el-form-item label="外部交易号:">
                      <span>{{BaseData.out_transaction_id}}</span>
                    </el-form-item>
                    <el-form-item label="退款金额(元):">
                      <span>{{BaseData.refund_price / 100}}</span>
                    </el-form-item>
                    <el-form-item label="订单总金额(元):">
                      <span>{{BaseData.order_price / 100}}</span>
                    </el-form-item>
                    <el-form-item label="用户退款金额(元):">
                      <span>{{BaseData.payer_refund / 100}}</span>
                    </el-form-item>
                    <el-form-item label="币种:">
                      <span>{{BaseData.currency==1?"CNY":"USD"}}</span>
                    </el-form-item>
                    <el-form-item label="订单状态:">
                      <el-tag
                        :type="getTagType(BaseData.state)"
                      >{{BaseData.state==1?"已申请":(BaseData.state==5?"退款中":(BaseData.state==10?"已拒绝":(BaseData.state==11?"已取消":(BaseData.state==20?"已退款":(BaseData.state==30?"退款失败":(BaseData.state==40?"其他":"未知"))))))}}</el-tag>
                    </el-form-item>
                    <el-form-item label="退款渠道:">
                      <span>{{BaseData.channel=="ORIGINAL"?"原路退款":(BaseData.channel=="BALANCE"?"退回到余额":(BaseData.channel=="OTHER_BALANCE"?"原账户异常退到其他余额账户":(BaseData.channel=="OTHER_BANKCARD"?"原银行卡异常退到其他银行卡":"其他")))}}</span>
                    </el-form-item>
                    <el-form-item label="微信返回状态:">
                      <span>{{BaseData.out_status=="SUCCESS"?"退款成功":(BaseData.out_status=="CLOSED"?"退款关闭":(BaseData.out_status=="PROCESSING"?"退款处理中":(BaseData.out_status=="ABNORMAL"?"退款异常":"未知")))}}</span>
                    </el-form-item>
                    <el-form-item label="创建时间:">
                      <span>{{BaseData.create_time}}</span>
                    </el-form-item>
                    <el-form-item label="退款成功时间:">
                      <span>{{BaseData.success_time}}</span>
                    </el-form-item>
                    <el-form-item label="用户id:">
                      <span>{{BaseData.user_id}}</span>
                    </el-form-item>
                    <el-form-item label="用户手机号:">
                      <span>{{BaseData.phone}}</span>
                    </el-form-item>
                    <el-form-item label="客户ip:">
                      <span>{{BaseData.ip}}</span>
                    </el-form-item>
                    <el-form-item label="结果信息:">
                      <span>{{BaseData.msg}}</span>
                    </el-form-item>
                    <el-form-item label="退款入账账户:">
                      <span>{{BaseData.user_received_account==null?"无":BaseData.user_received_account}}</span>
                    </el-form-item>
                    <el-form-item label="退款截图链接:">
                      <span>{{BaseData.refund_screenshot_url==null?"未退款/历史数据":BaseData.refund_screenshot_url}}</span>
                    </el-form-item>
                    <el-col :span="24">
                      <el-form-item label="回调数据:">
                        <el-col :span="24">
                          <el-table
                            :data="CallbackData"
                            style="width: 100%"
                            :default-sort="{prop: 'date', order: 'descending'}"
                          >
                            <el-table-column
                              prop="type"
                              label="类型"
                              sortable
                              min-width="220"
                              align="center"
                            ></el-table-column>
                            <el-table-column prop="log" label="返回信息" min-width="400" align="center"></el-table-column>
                            <el-table-column
                              prop="create_time"
                              label="时间"
                              sortable
                              min-width="180"
                              align="center"
                            ></el-table-column>
                          </el-table>
                        </el-col>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-dialog>
      <!-- 统计弹框 -->
      <el-dialog
        title="审核"
        class="StatiseBox"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >
        <!-- 退款成功弹框 -->
        <el-dialog
          append-to-body
          title="退款"
          class="RefundBox"
          :visible.sync="RefundVisible"
          width="40%"
          :before-close="handleRefundClose"
          :close-on-click-modal="false"
        >
          <el-form label-width="auto" :model="form" ref="FormRef" :rules="rules">
            <el-form-item label="退款截图:" prop="refund_screenshot_id">
              <template>
                <el-upload
                  class="avatar-uploader"
                  :action="uploadUrl"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                  :headers="{ Authorization: token }"
                  :multiple="true"
                  :limit="1"
                  :file-list="fileList"
                >
                  <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </template>
            </el-form-item>
            <div class="sunTitle" style="margin-bottom:20px;">
              <i class="el-icon-warning-outline"></i>
              <span style="font-Size:12px">实际退款操作请在第三方系统完成，例如微信支付后台，完成后需截图退款结果，回到本系统登记完成退款操作。</span>
            </div>

            <div class="text-c mb-50">
              <el-button @click="cancelFn">取 消</el-button>
              <el-button type="primary" @click="submitFn">完成退款</el-button>
            </div>
          </el-form>
        </el-dialog>
        <ul class="first">
          <li>充值金额</li>
          <li>消费金额</li>
          <li>退款金额</li>
          <li>用户余额</li>
        </ul>
        <ul class="second">
          <li></li>
          <li></li>
          <li>
            <div class="top">
              <span class="left">已退金额</span>
              <span class="right">待退金额</span>
            </div>
          </li>
          <li></li>
        </ul>
        <ul class="third">
          <li>
            <span>￥{{(StatisticsData.user_recharge_sum_price/100).toFixed(2)}}</span>
          </li>-
          <li>
            <span>￥{{(StatisticsData.user_sum_consumption_amount/100).toFixed(2)}}</span>
          </li>-
          <li>
            <div class="bottom">
              （
              <span class="left">￥{{StatisticsData.user_sum_refund_price/100}}</span>
              +
              <span class="right">￥{{StatisticsData.user_sum_refund_price_wait/100}}</span>
              ）
            </div>
          </li>
          <span v-if="StatisticsData.sum==0">=</span>
          <span v-else>!=</span>
          <li>
            <span>￥{{StatisticsData.user_balance/100}}</span>
          </li>
        </ul>
        <!-- 检测结果正常 -->
        <div class="check" v-if="StatisticsData.sum==0">
          <i class="el-icon-success"></i>
          <span class="result">系统检测正常</span>
        </div>
        <div class="check" v-else>
          <i class="el-icon-error"></i>
          <span class="result">系统检测异常</span>
        </div>
        <!-- 检测结果异常 -->
        <!-- 退款按钮 -->
        <div class="btn">
          <el-button
            class="duck-green-button btnPadding"
            @click="AgreeFn()"
            style="border-radius: 2px;border:none;padding: 5px 10px"
          >同意退款</el-button>
          <el-button
            class="duck-red-button btnPadding"
            @click="RefuseFn()"
            style="border-radius: 2px;border:none;padding: 5px 10px"
          >拒绝退款</el-button>
        </div>
        <!-- 汇总计算方式 -->
        <div class="sunTitle">
          <i class="el-icon-warning-outline"></i>
          <span style="font-Size:12px">以上统计数据与系统检测结果，只是结合系统内部数据给出的评估，仅限参考。</span>
        </div>
      </el-dialog>
    </div>
  </div>
</template> 
  <script>
import { pickerOptions, queryFormLimit } from "@/common.js";
import pagination from "@/components/pagination.vue";
import {
  GetHostApi,
  GetRefundInfoApi,
  GetRefundOrderlistApi,
  GetRefundSuccessApi,
  GetRefuseRefundApi,
  RefundSortListApi,
  UsersStatisticsApi
} from "@/request/api";
import Crumb from "@/views/home/<USER>";
import { debounce } from "lodash";
export default {
  components: {
    pagination,
    Crumb
  },
  data() {
    return {
      ViewdialogVisible: false,
      form: {
        refund_screenshot_id: ""
      },
      rules: {},
      uploadUrl: "",
      fileList: [],
      host: "", // 主机域名
      imageUrl: "",
      token: "",
      pid: null,
      user_corp_id: null,
      picker_options: pickerOptions,
      query_form_limit: queryFormLimit,
      RefundVisible: false,
      activeNames: ["1"],
      //选项卡
      activeName: "first",
      // 基本信息
      BaseData: [],
      // 查询
      div_loading: true,
      tableData: [],
      CallbackData: [],
      options: [
        { value: 1, label: "下单" },
        { value: 2, label: "已冻结" },
        { value: 3, label: "充电中" },
        { value: 4, label: "充电完成" },
        { value: 5, label: "完成" },
        { value: 6, label: "异常" },
        { value: 20, label: "其他" }
      ],
      // 显示当前页
      isShowCurrPage: true,
      // 运营商编号
      OperatorOptions: [],
      //站id
      Stationoptions: [],
      // 弹框信息变量

      query_form: {
        id: "",
        user_id: null,
        order_id: "",
        out_refund_id: "",
        out_transaction_id: "",
        state: null,
        time_range: "",
        page: 1,
        limit: 10,
        order_name: "a.create_time",
        order_type: "desc"
      },
      current_page: null,
      sort_list: [],
      phone: "",
      value: "",
      corp_id: null,
      //退款订单状态
      stateOption: [
        { value: 1, label: "已申请" },
        { value: 5, label: "退款中" },
        { value: 10, label: "已拒绝" },
        { value: 11, label: "已取消" },
        { value: 20, label: "已退款" },
        { value: 30, label: "退款失败" },
        { value: 40, label: "其他" }
      ],
      dialogVisible: false,
      StatisticsData: [],
      //退款订单号
      refund_id: "",
      //退款状态
      refund_state: null
    };
  },

  methods: {
    cancelFn() {
      this.RefundVisible = false;
      this.imageUrl = null;
      this.fileList = [];
    },
    async submitFn() {
      let res = await GetRefundSuccessApi({
        refund_id: this.refund_id,
        refund_screenshot_id: this.form.refund_screenshot_id
      });
      if (!res) return;
      if (res.code == 200) {
        this.$message({
          type: "success",
          message: "成功!"
        });
        this.dialogVisible = false;
        this.RefundVisible = false;
        // 重新获取列表接口
        this.getTableData(this.current_page);
      }
    },
    handleRefundClose() {
      this.RefundVisible = false;
    },
    handleAvatarSuccess(res, file, fileList) {
      console.log("handleAvatarSuccess11", fileList);
      this.imageUrl = URL.createObjectURL(file.raw);
      console.log("res", res);
      this.form.refund_screenshot_id = res.data.id;
      this.fileList = [];
    },
    beforeAvatarUpload(file) {
      this.imageUrl = " ";
      const isJPG = file.type === "image/jpeg" || file.type === "image/png";

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 jpg 格式 或 png格式!");
      }
    },
    getTagType(state) {
      switch (state) {
        case 1: // 已申请
          return "danger";
        case 5: // 退款中
          return "success";
        case 10: // 已拒绝
          return "info";
        case 11: // 已取消
          return "primary"; // 假设已取消时类型为 primary
        case 20: // 已退款
          return "success";
        case 30: // 退款失败
          return "warning"; // 假设退款失败时类型为 warning
        default:
          // 其他状态
          return "default"; // 假设其他状态为默认类型
      }
    },
    // 排序
    async SortListInfoFn() {
      let res = await RefundSortListApi();
      if (!res) return;
      this.sort_list = res.data.sort_list;
      this.query_form.order_name = res.data.order_name;
      this.query_form.order_type = res.data.order_type;
    },
    reset_query_form(e = false) {
      this.query_form = {
        id: "",
        user_id: null,
        order_id: "",
        out_refund_id: "",
        out_transaction_id: "",
        state: null,
        start_time: "",
        end_time: "",
        page: 1,
        limit: 10,
        order_name: "a.create_time",
        order_type: "desc"
      };
      if (e) {
        this.getTableData(1);
      }
    },
    table_tiaozhuan(val) {
      this.query_form.status = [];
      this.div_loading = true;
      this.getTableData(val);
    },
    // 详情
    async RefundInfo(row) {
      let res = await GetRefundInfoApi({ refund_id: row.id });
      if (!res) return;
      if (res.code == 200) {
        this.ViewdialogVisible = true;
      }
      this.BaseData = res.data;
      this.CallbackData = res.data.callback_list;
      console.log("回调信息", this.CallbackData);
      console.log("this.form", this.form);
    },
    // 同意退款
    AgreeFn: debounce(function() {
      this.RefundVisible = true;
      // this.$confirm("此操作将同意退款请求, 是否同意?", "提示", {
      //   confirmButtonText: "同意",
      //   cancelButtonText: "取消",
      //   type: "warning"
      // })
      //   .then(async () => {
      //     let res = await GetAgreeRefundApi({ refund_id: this.refund_id });
      //     if (!res) return;
      //     this.$message({
      //       type: "success",
      //       message: "同意退款成功!"
      //     });
      //     this.dialogVisible = false;
      //     // 重新获取列表接口
      //     this.getTableData(this.current_page);
      //   })
      //   .catch(() => {
      //     this.$message({
      //       type: "info",
      //       message: "已取消"
      //     });
      //   });
    }, 200),
    // 拒绝退款
    RefuseFn: debounce(function() {
      this.$confirm("此操作将拒绝退款请求, 是否同意?", "提示", {
        confirmButtonText: "拒绝",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          let res = await GetRefuseRefundApi({ refund_id: this.refund_id });
          if (!res) return;
          this.$message({
            type: "success",
            message: "拒绝退款成功!"
          });
          // 重新获取列表接口
          this.getTableData(this.current_page);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    }, 200),
    // 获取列表请求
    async getTableData(page = 1) {
      console.log("this.query_form", this.query_form);
      this.div_loading = true;
      this.query_form.page = page;
      let form = this.query_form;
      let Res = await GetRefundOrderlistApi(form);
      if (!Res) return;
      this.tableData = Res.data.data;
      this.query_form.total = Res.data.total;
      this.current_page = Res.data.current_page;
      this.div_loading = false;
    },
    async StatisticsFn(row) {
      console.log("row", row);
      let res = await UsersStatisticsApi({ id: row.user_id });
      if (!res) return;
      if (res.code == 200) {
        this.dialogVisible = true;
      }
      this.StatisticsData = res.data;
      this.refund_id = row.id;
      console.log("refund_id", this.refund_id);
      console.log("this.StatisticsData", this.StatisticsData);
    },
    //统计关闭
    handleClose() {
      this.dialogVisible = false;
    }
  },
  async created() {
    console.log("imageUrl", this.imageUrl);
    this.token = localStorage.getItem("token");
    console.log("token", this.token);
    this.pid = parseInt(localStorage.getItem("pid"));
    this.user_corp_id = parseInt(localStorage.getItem("user_corp_id"));
    this.getTableData();
    this.SortListInfoFn();
    // 获取主机域名
    try {
      const res = await GetHostApi();
      this.host = res.data.host;
      // 拼接上传图片的完整地址
      this.uploadUrl = `${this.host}/admin/Common/upload_img`;
      console.log("...", this.uploadUrl);
    } catch (error) {
      console.error("获取主机域名失败:", error);
      return false; // 取消上传
    }
  }
};
</script>
  
  <style lang="less" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.Equipment {
  height: 100%;

  .MenuIcon {
    .IconBox {
      border: 1px solid #dcdfe6;
      display: inline-block;
      width: 50px;
      text-align: center;
      i {
        font-size: 22px;
        vertical-align: middle;
      }
    }
    .DropdownBtn {
      border: 1px solid #dcdfe6;
      display: inline-block;
      width: 50px;
      text-align: center;
      box-sizing: border-box;
    }
  }

  .crumb {
  }

  .form {
    box-shadow: 3px 0 5px #ccc;

    // 头部
    .header {
      display: flex;
      height: 40px;
      line-height: 40px;
      background-color: #f5f7fa;
      margin-bottom: 10px;

      li {
        border: 1px solid #f5f7fa;
        background-color: #fff;
        border-radius: 5px;
        padding: 0 10px;
        cursor: pointer;
        color: #cacdd2;

        &.active {
          color: #009688;
        }
      }
    }

    // 表格
    .Table_Form {
      margin-left: 10px;
      .ellipsis-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ellipsis-id {
        white-space: nowrap;
      }
      .el-input-number--small {
        width: 50px;
      }
      .amountBox {
        display: flex;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        .sum_footer_totalMoney {
          display: flex;
          margin-left: 10px;
        }
        .sum_footer_totalElect {
          display: flex;
          margin-right: 20px;
          width: 238px;
        }
        .sum_footer_money {
          width: 122px;
        }
        .sum_footer_money_num {
          color: #000;
          font-size: 20px;
          font-weight: 600;
        }

        .sum_footer_electricity_num {
          color: #000;
          font-size: 20px;
          font-weight: 600;
        }
      }
    }
    .Page {
      height: 45px;
      background-color: #fff;
      padding-top: 10px;
      padding-bottom: 10px;

      ::v-deep .el-pagination {
        text-align: center;
      }

      ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #009688;
      }
    }

    .el-radio {
      margin-right: 10px;
    }
    .ViewBox {
      ::v-deep .el-tabs__item:hover {
        color: #009688 !important;
      }
      ::v-deep .is-active {
        color: #009688 !important;
      }
      ::v-deep .el-tabs__active-bar {
        background-color: #009688;
      }

      .demo-table-expand {
        font-size: 0;
      }
      .demo-table-expand label {
        width: 90px;
        color: #99a9bf;
      }
      .demo-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
      }
      .second {
        .btn {
          margin-left: 20px;
          width: 50px;
          border: 1px solid #eee;
          border-radius: 4px;
          padding: 5px;
          background-color: #fff;
        }
      }
    }
    .StatiseBox {
      .RefundBox {
      }

      .first {
        display: flex;
        justify-content: space-between;
        text-align: center;
        margin-bottom: 10px;
        li {
          flex: 1;
          font-size: 20px;
          color: #000;
        }
        li:nth-of-type(3) {
          flex: 2;
        }
      }
      .second {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        text-align: center;
        li {
          flex: 1;
          color: #000;
          font-size: 16px;
        }
        li:nth-of-type(3) {
          flex: 2;
          .top {
            padding: 0 25px;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            font-size: 16px;
          }
        }
      }
      .third {
        display: flex;
        justify-content: space-between;
        align-items: center;

        text-align: center;
        li {
          flex: 1;
          color: #000;
          font-size: 16px;
        }
        li:nth-of-type(3) {
          flex: 2;
          .bottom {
            padding: 0 20px;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            font-size: 16px;
          }
        }
      }
      .check {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          font-size: 30px;
          color: #000;
        }
        .result {
          font-size: 20px;
          color: #000;
        }
      }
      .btn {
        margin-top: 15px;
        display: flex;
        justify-content: center;
        .btnPadding {
          margin: 0 50px;
        }
      }
      .sunTitle {
        margin-top: 10px;
      }
    }
  }
}
</style>