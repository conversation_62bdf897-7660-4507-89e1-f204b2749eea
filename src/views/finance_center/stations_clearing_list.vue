<!--suppress JSUnresolvedReference, JSValidateTypes -->
<template>
  <div class="Equipment">
    <!-- 面包屑 -->
    <div class="crumb">
      <Crumb ref="crumb"></Crumb>
    </div>
    <div class="form" v-loading="div_loading">
      <!-- 搜索栏 -->
      <div class="search text-c">
        <el-row>
          <el-col :span="24" class="mt-10">
            <span class="select-box ml-5">
              账单日期:
              <el-date-picker
                value-format="yyyy-MM-dd"
                style="width: 220px;"
                v-model="query_form.time_range"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :clearable="false"
                :picker-options="picker_options"
              ></el-date-picker>
            </span>

            <span class="select-box ml-5">
              状态:
              <el-select
                v-model="query_form.status"
                filterable
                style="width: 120px;"
                size="small"
                placeholder="状态"
              >
                <el-option
                  v-for="(v,k) in order_clearing_status_list"
                  :key="'查询表单订单清分状态'+k"
                  :label="v"
                  :value="k"
                ></el-option>
              </el-select>
            </span>

            <span class="select-box ml-5" v-if="pid <= 0">
              运营商:
              <el-select
                v-model="query_form.corp_id"
                filterable
                style="width: 150px;"
                size="small"
                @change="OperatorFn"
                placeholder="运营商"
              >
                <el-option label="全部" :value="0"></el-option>
                <el-option
                  v-for="(v,k) in corp_list_all"
                  :key="'查询表单运营商'+k"
                  :label="v.name"
                  :value="v.id"
                ></el-option>
              </el-select>
            </span>

            <span class="select-box ml-5">
              场站:
              <el-select
                v-model="query_form.station_id"
                filterable
                style="width: 150px;"
                size="small"
                placeholder="场站"
              >
                <el-option label="全部" :value="0"></el-option>
                <el-option
                  v-for="v in stations_list_all"
                  :key="'查询表单场站'+v.id"
                  :label="v.name"
                  :value="v.id"
                ></el-option>
              </el-select>
            </span>

            <el-button @click="getTableData()" size="small" class="duck-green-button ml-5">查询</el-button>
            <el-button @click="reset_query_form(true)" type="info" size="small" class="ml-5">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 操作栏 -->
      <div>
        <el-card
          class="mt-10 mb-10 text-c"
          shadow="always"
          :body-style="{ padding: '8px' }"
          style="background: #f5fafe;"
        >
          <div class="cl">
            <el-button @click="generateSummaryCorp()" size="small" type="primary" class="f-l">生成汇总单</el-button>
            <el-button @click="generateStatementStation()" size="small" type="primary" class="f-l el-button--warning">生成场站对账单</el-button>

            <span style="font-size: 16px;color: #ff0000;line-height: 30px;font-weight: bold;">
              <span>充电费：{{sum_clearing_electricity_price/10000}}元</span>
              <span>&nbsp;&nbsp;&nbsp;</span>
              <span>服务费：{{sum_clearing_ser_price/10000}}元</span>
            </span>

            <el-tag type="info" class="f-r" effect="dark">共：{{query_form.total}} 条</el-tag>
            <span class="f-r mr-5">
              每页:
              <el-select
                v-model="query_form.limit"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option
                  v-for="v in query_form_limit"
                  :key="'查询表单'+v"
                  :label="v.toString()"
                  :value="v"
                ></el-option>
              </el-select>条
            </span>
            <span class="f-r mr-5">
              排序:
              <el-select
                v-model="query_form.order_name"
                size="small"
                class="width-120"
                @change="getTableData()"
              >
                <el-option
                  v-for="item in sort_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select
                v-model="query_form.order_type"
                size="small"
                class="width-100"
                @change="getTableData()"
              >
                <el-option label="降序" value="desc"></el-option>
                <el-option label="升序" value="asc"></el-option>
              </el-select>
            </span>
          </div>
        </el-card>
      </div>

      <!-- 大表格 -->
      <div class="Table_Form">
        <el-table
          style="width:100%"
          :data="table_data"
          @selection-change="handleTableSelection"
          border
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column prop="stations_name" label="场站" min-width="150" align="center"></el-table-column>
          <el-table-column prop="corp_name" label="运营商" min-width="150" align="center">
            <template #default="{row}">
              <div v-for="(v,k) in row.corp_name" :key="'表格-场站-'+k">{{v}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="bill_date" label="账单日期" min-width="120" align="center">
            <template #default="{row}">
              <div>{{ row.bill_date[0] + ' 至 ' + row.bill_date[row.bill_date.length - 1] }}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="clearing_electricity_price"
            label="应分充电费"
            width="100"
            align="center"
          >
            <template #default="{row}">
              <div style="color: #ff0000">{{row.clearing_electricity_price/10000}}元</div>
            </template>
          </el-table-column>
          <el-table-column prop="clearing_ser_price" label="应分服务费" width="100" align="center">
            <template #default="{row}">
              <div style="color: #ff0000">{{row.clearing_ser_price/10000}}元</div>
            </template>
          </el-table-column>

          <el-table-column prop="clearing_ser_price" label="状态" width="100" align="center">
            <template #default="{row}">
              <div>
                <el-tag v-if="row.status===1||row.status==='1'" type size="mini" effect="dark">待结算</el-tag>
                <el-tag
                  v-else-if="row.status===2||row.status==='2'"
                  type="success"
                  size="mini"
                  effect="dark"
                >已结算</el-tag>
                <el-tag v-else-if="row.status==='1,2'" type="warning" size="mini" effect="dark">部分结算</el-tag>
                <el-tag v-else type="info" size="mini" effect="dark">未知</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="clearing_time" label="结算时间" min-width="160" align="center">
            <template #default="{row}">
              <div v-for="(v,k) in row.clearing_time" :key="'表格-结算时间-'+k">{{v}}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        :page-size="query_form.limit"
        :total="query_form.total"
        :current-page="query_form.page"
        @page-change="table_tiaozhuan"
      ></pagination>

      <el-drawer
        v-if="show_summary"
        title="汇总单"
        :visible.sync="show_summary"
        direction="btt"
        size="95%"
      >
        <div>
          <stations_clearing_summary
            :statement_data="selection_table_data"
            :date="query_form.time_range"
            :statement_type="statement_type"
          ></stations_clearing_summary>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { pickerOptions, queryFormLimit } from "@/common.js";
import pagination from "@/components/pagination.vue";
import {
  GetCorpListAllApi,
  GetOrderClearingStatusListApi,
  StationGetStationsApi,
  StationsClearingListApi,
  StationsClearingSortListInfoApi,
  GenerateStationsClearingStatisticsApi
} from "@/request/api";
import Crumb from "@/views/home/<USER>";
import stations_clearing_summary from "./stations_clearing_summary";

export default {
  components: {
    stations_clearing_summary,
    pagination,
    Crumb
  },
  data() {
    return {
      div_loading: true,
      picker_options: pickerOptions,

      query_form: {},
      y_query_form: {
        time_range: this.commonFunc.get_date_range(-31),
        status: "",
        order_id: "",
        station_id: "",
        corp_id: "",
        page: 1,
        limit: 10,
        order_name: "a.bill_date",
        order_type: "desc"
      },
      pid: null,
      query_form_limit: queryFormLimit,
      table_data: [],
      sort_list: [],

      order_clearing_status_list: [],
      corp_list_all: [],
      stations_list_all: [],

      selection_table_data: [],

      show_summary: false,
      statement_type: "",
      show_settle_accounts: false,
      settle_accounts_loading: false,
      clearing_time: this.commonFunc.get_now_datetime(),

      sum_clearing_electricity_price: 0,
      sum_clearing_ser_price: 0
    };
  },
  created() {
    this.pid = parseInt(localStorage.getItem("pid"));
    if (this.pid > 0) {
      this.OperatorFn();
    }
    this.reset_query_form(true);
    // this.getStationsListAll();
    this.getCorpListAll();
    this.SortListInfoFn();
    this.getOrderClearingStatusList();
  },
  methods: {
    async OperatorFn(e) {
      this.stations_list_all = [];
      this.$set(this.query_form, "station_id", null);
      let res = null;
      if (this.pid > 0) {
        res = await StationGetStationsApi();
        if (!res) return;
      } else {
        res = await StationGetStationsApi({ corp_id: e });
        if (!res) return;
      }

      this.stations_list_all = res.data;
    },
    handleTableSelection(v) {
      this.selection_table_data = v.map(item => {
        const {
          stations_name,
          corp_name,
          bill_date,
          clearing_electricity_price,
          clearing_ser_price,
          clearing_time
        } = item;
        return {
          stations_name,
          corp_name,
          bill_date,
          clearing_electricity_price,
          clearing_ser_price,
          clearing_time
        };
      });
      console.log(
        "handleTableSelection-selection_table_data",
        this.selection_table_data
      );
    },
    generateSummaryCorp() {
      if (this.selection_table_data.length === 0) {
        this.$message.error("请先选择场站");
        return;
      }
      this.show_summary = true;
    },
    async generateStatementStation() {
      if (this.selection_table_data.length === 0) {
        this.$message.error("请先选择场站");
        return;
      }

      // 准备请求参数
      const params = {
        time_range: this.query_form.time_range,
        station_data: this.selection_table_data
      };

      try {
        // 发起请求生成对账单统计
        const res = await GenerateStationsClearingStatisticsApi(params);
        this.commonFunc.show_log("GenerateStationsClearingStatisticsApi-res", res);

        if (!res) {
          this.$message.error("生成对账单失败");
          return;
        }

        this.$message.success("对账单生成成功");
      } catch (error) {
        this.commonFunc.show_log("GenerateStationsClearingStatisticsApi-error", error);
        this.$message.error("生成对账单时发生错误");
      }
    },
    // async getStationsListAll() {
    //   let res = await GetStationsListAllApi();
    //   this.commonFunc.show_log("GetStationsListAllApi-res", res);
    //   if (!res) return;
    //   this.stations_list_all = res.data;
    // },
    async getCorpListAll() {
      let res = await GetCorpListAllApi();
      this.commonFunc.show_log("GetCorpListAllApi-res", res);
      if (!res) return;
      this.corp_list_all = res.data;
    },
    async getOrderClearingStatusList() {
      let res = await GetOrderClearingStatusListApi();
      this.commonFunc.show_log("GetOrderClearingStatusListApi-res", res);
      if (!res) return;
      this.order_clearing_status_list = res.data;
    },
    async SortListInfoFn() {
      let res = await StationsClearingSortListInfoApi();
      this.commonFunc.show_log("StationsClearingSortListInfoApi-res", res);
      if (!res) return;
      this.sort_list = res.data.sort_list;
      this.query_form.order_name = res.data.order_name;
      this.query_form.order_type = res.data.order_type;
    },
    reset_query_form(e = false) {
      this.query_form = Object.assign({}, this.y_query_form);
      if (e) {
        this.getTableData(1);
      }
    },
    table_tiaozhuan(val) {
      this.div_loading = true;
      this.getTableData(val);
    },

    async getTableData(page = 1) {
      this.div_loading = true;
      this.$set(this.query_form, "page", page);
      let form = this.query_form;

      let res = await StationsClearingListApi(form);
      this.commonFunc.show_log("StationsClearingListApi-res", res);
      if (!res) {
        this.div_loading = false;
        return;
      }
      this.table_data = res.data.data;
      this.query_form.total = res.data.total;
      this.sum_clearing_electricity_price =
        res.data.sum_clearing_electricity_price;
      this.sum_clearing_ser_price = res.data.sum_clearing_ser_price;
      this.div_loading = false;
    }
  }
};
</script>

<style lang="less" scoped>
.Equipment {
  height: 100%;

  .MenuIcon {
    .IconBox {
      border: 1px solid #dcdfe6;
      display: inline-block;
      width: 50px;
      text-align: center;
      i {
        font-size: 22px;
        vertical-align: middle;
      }
    }
    .DropdownBtn {
      border: 1px solid #dcdfe6;
      display: inline-block;
      width: 50px;
      text-align: center;
      box-sizing: border-box;
    }
  }

  .crumb {
  }

  .form {
    box-shadow: 3px 0 5px #ccc;

    // 头部
    .header {
      display: flex;
      height: 40px;
      line-height: 40px;
      background-color: #f5f7fa;
      margin-bottom: 10px;

      li {
        border: 1px solid #f5f7fa;
        background-color: #fff;
        border-radius: 5px;
        padding: 0 10px;
        cursor: pointer;
        color: #cacdd2;

        &.active {
          color: #009688;
        }
      }
    }

    // 表格
    .Table_Form {
      margin-left: 10px;
      .ellipsis-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ellipsis-id {
        white-space: nowrap;
      }
      .el-input-number--small {
        width: 50px;
      }
      .amountBox {
        display: flex;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        .sum_footer_totalMoney {
          display: flex;
          margin-left: 10px;
        }
        .sum_footer_totalElect {
          display: flex;
          margin-right: 20px;
          width: 238px;
        }
        .sum_footer_money {
          width: 122px;
        }
        .sum_footer_money_num {
          color: #000;
          font-size: 20px;
          font-weight: 600;
        }

        .sum_footer_electricity_num {
          color: #000;
          font-size: 20px;
          font-weight: 600;
        }
      }
    }
    .Page {
      height: 45px;
      background-color: #fff;
      padding-top: 10px;
      padding-bottom: 10px;

      ::v-deep .el-pagination {
        text-align: center;
      }

      ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #009688;
      }
    }

    .el-radio {
      margin-right: 10px;
    }
  }
}
</style>